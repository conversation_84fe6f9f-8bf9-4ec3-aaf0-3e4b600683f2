import { HttpClient } from '@angular/common/http';
import { Component, HostListener, OnInit, OnDestroy } from '@angular/core';
import { Router, Event, NavigationStart, NavigationEnd, NavigationError } from '@angular/router';
import { MsalService } from '@azure/msal-angular';
import { ActiveX, environment } from '../../environments/environment';
import { UserInfo } from '../Authentication/UserInfo';
import { UserRoleModel } from '../Models/UserModel';
import { MoudleResponsibilityModel, UserRoleMasterModel } from '../Models/UserRoleModel';
import { AuthService } from '../Services/auth.service';
import { LoadingService } from '../Services/loadingService';
import { PublicClientApplication } from '@azure/msal-browser';
import { BarcodeScannerService } from '../Features/BarcodeLabelManagement/services/BarcodeScannerService';
import { Subscription } from 'rxjs';
import { ScannerMode } from '../Models/Enums';
import { CommonService } from '../Services/CommonService';
import { LoggingService } from '../Services/logging.service';
@Component({
  selector: 'app-home',
  templateUrl: './Home.component.html',
  styleUrls: ['./Home.component.css']
})
export class HomeComponent implements OnInit, OnDestroy {
  ApiUrl = environment.Api_Url;
  isCollapsed = true; // Start with collapsed sidebar by default
  isUserLoggedIn = false;
  ForceLogoutSession = false;
  AzureUserName = "";
  UserID = "";
  aquireTokenInterval: any;
  DivisionName = environment.DivisionName;
  UserRoles: UserRoleModel = new UserRoleModel();
  UserRoleList: UserRoleMasterModel[] = [];
  MoudleResponsibilityList: MoudleResponsibilityModel[] = []
  count = 0;

  openMap: { [name: string]: boolean } = {
    sub1: false,
    sub2: false,
    sub3: false,
    sub4: false,
    sub5: false,
    sub6: false,
    sub7: false,
    sub8: false,
    sub9: false,
    sub10: false,
    sub11: false,
    sub12: false,
    sub13: false,
    sub14: false,
    sub15: false,
    sub16: false,
    sub17: false,
    sub18: false,
    sub19: false,
    sub20: false,
    sub21: false,
    sub22: false,
    sub23: false,
  };
  openReportsMap: { [name: string]: boolean } = {
    sub1: false,
    sub2: false,
    sub3: false,
    sub4: false,
    sub5: false,
  };
  IsMenuLoaded = false;
  currentRoute: string;
  isIframe = false;
  public innerWidth: any;
  private subscription: Subscription = new Subscription();
  private isInitialized: boolean = false;

  @HostListener('window:resize', ['$event'])
  onResize(event: any) {
    this.innerWidth = window.innerWidth;
  }
  constructor(private authService: AuthService, private router: Router, private msalService: MsalService, public http: HttpClient, private loader: LoadingService,
    private barcodeService: BarcodeScannerService, private commonService: CommonService, private logger: LoggingService
  ) {
    this.currentRoute = "";
    this.acquireTokenSilently();
    this.acquireTokenSilentlyOnInterval();
    this.router.events.subscribe((event: Event) => {
      if (event instanceof NavigationStart) {
        // Show progress spinner or progress bar
        this.logger.debug('Route change detected', { url: event.url });
        this.acquireTokenSilently();
        this.acquireTokenSilentlyOnInterval();
      }

      if (event instanceof NavigationEnd) {
        // Hide progress spinner or progress bar
        this.currentRoute = event.url;
        this.logger.debug('Navigation completed', { url: event.url });
        this.changeOfRoutes();
      }

      if (event instanceof NavigationError) {
        // Hide progress spinner or progress bar
        // Present error to user
        this.logger.error('Navigation error occurred', event.error, 'HomeComponent');
      }
    });
  }

  openHandler(value: string): void {
    for (const key in this.openMap) {
      if (key !== value) {
        this.openMap[key] = false;
      }
    }
  }
  openReportsHandler(value: string): void {
    for (const key in this.openReportsMap) {
      if (key !== value) {
        this.openReportsMap[key] = false;
      }
    }
  }

  /**
   * Refresh user permissions by clearing cache and fetching fresh data
   */
  refreshPermissions() {
    this.logger.info('User permissions refresh initiated');

    // Check session validity before refreshing permissions
    this.authService.getAppSessionTime("appSessionExpiry").then(sessionExpiry => {
      if (sessionExpiry === null || this.authService.ForceLogoutSession) {
        this.logger.session('expire', 'Session expired, cannot refresh permissions - triggering logout');
        this.logout();
        return;
      }

      // Session is valid, proceed with refresh
      this.commonService.clearPermissionsCache(UserInfo.EmailID);
      this.GetAllResponsibility(true);
    }).catch(error => {
      this.logger.error('Session validation failed during permissions refresh', error, 'HomeComponent');
      this.logout();
    });
  }
  GetAllResponsibility(forceRefresh: boolean = false) {
    const startTime = Date.now();
    this.logger.debug('Loading user permissions', { forceRefresh, attempt: this.count + 1 });
    this.loader.show();

    this.commonService.getUserPermissions(UserInfo.EmailID, forceRefresh).subscribe({
      next: (data) => {
        const duration = Date.now() - startTime;
        this.logger.info('User permissions loaded successfully', {
          responsibilitiesCount: data.responsibilities.length,
          userRolesCount: data.userRoles.length,
          duration,
          forceRefresh
        });

        // Process responsibilities data
        let uniqueModule = [...new Set(data.responsibilities.map(item => item.Module))];
        this.MoudleResponsibilityList = []; // Clear existing data

        uniqueModule.forEach(el => {
          var item = new MoudleResponsibilityModel;
          item.Module = el;
          item.Responsibilities = data.responsibilities.filter(x => x.Module == el);
          this.MoudleResponsibilityList.push(item);
        });

        // Process user roles data
        this.UserRoleList = data.userRoles;
        UserInfo.UserRolesMaster = data.userRoles;

        // Map user responsibilities
        this.mapUserResponsibilities(data.userRoles);

        this.IsMenuLoaded = true;
        if (forceRefresh) {
          //load the page again
          this.router.navigate([this.currentRoute]);
        }
        this.loader.hide();
      },
      error: (error) => {
        this.logger.error('Error loading permissions', error, 'HomeComponent');
        this.loader.hide();

        // Check if it's a session-related error
        if (error.message?.includes('Session expired') || error.message?.includes('Authentication failed')) {
          this.logger.session('expire', 'Session expired during permissions load, triggering logout');
          // Don't retry for session errors, just logout
          this.logout();
          return;
        }

        // For other errors, implement retry logic
        this.count++;
        if (this.count < 2) {
          this.logger.warn('Retrying permissions load without cache', { attempt: this.count }, 'HomeComponent');
          // Retry without cache on first failure
          this.GetAllResponsibility(true);
        } else {
          // Show error message after retries
          this.logger.error('Failed to load user permissions after retries', { attempts: this.count }, 'HomeComponent');
          // Could show a user-friendly error message here
        }
      }
    });
  }

  private mapUserResponsibilities(userRoles: UserRoleMasterModel[]) {
    var myResponsibilityList: number[] = [];
    userRoles.forEach(el => {
      var m = el.Responsibilities.map(x => x.ResponsibilityId);
      m.forEach(x => {
        myResponsibilityList.push(x);
      });
    });

    this.MoudleResponsibilityList.forEach(el => {
      el.Responsibilities.forEach(x => {
        if (myResponsibilityList.includes(x.ResponsibilityId)) {
          x.IsChecked = true;
          el.IsChecked = true;
        } else {
          x.IsChecked = false;
        }
      });
    });

    UserInfo.MyRoles = this.MoudleResponsibilityList;
  }

  CheckModule(Module: string) {
    return this.authService.CheckModule(Module);

  }
  CheckResponsibility(Module: string, Responsibility: string) {
    return this.authService.CheckResponsibility(Module, Responsibility);
  }
  // getWithExpiry(key: string) {
  //   const itemStr = localStorage.getItem(key)

  //   // if the item doesn't exist, return null
  //   if (!itemStr) {
  //     return null
  //   }

  //   const item = JSON.parse(itemStr)
  //   const now = new Date()

  //   // compare the expiry time of the item with the current time
  //   if (now.getTime() > item.expiry) {
  //     // If the item is expired, delete the item from storage
  //     // and return null
  //     localStorage.removeItem(key);
  //     this.ForceLogoutSession = true;
  //     return null;
  //   }
  //   return item.value
  // }
  changeOfRoutes() {

    if (this.authService.getAppSessionTime("appSessionExpiry") == null || this.authService.getAppSessionTime("token") == null) {
      this.isUserLoggedIn = false;
    }
    if (this.isUserLoggedIn && (!localStorage.getItem('expiresOn') || !localStorage.getItem('userName') || !localStorage.getItem('homeAccountId'))) {
      this.authService.setSessionData()
    }
    if (this.authService.ForceLogoutSession) {
      this.logout();
    }
  }
  async ngOnInit() {
    this.logger.debug('HomeComponent initializing');

    this.isIframe = window !== window.parent && !window.opener; this.onResize("size");
    if (localStorage.getItem('token') == undefined || localStorage.getItem('expiresOn') == undefined || localStorage.getItem('userName') || localStorage.getItem('homeAccountId')) {
      this.logger.debug('Clearing incomplete session data');
      localStorage.removeItem('token');
      localStorage.removeItem('homeAccountId');
      localStorage.removeItem('expiresOn');
      localStorage.removeItem('userName');
    }
    if (this.authService.getAppSessionTime("appSessionExpiry") == null || this.authService.getAppSessionTime("token") == null) {
      this.isUserLoggedIn = false;
    }
    var userinfo = this.msalService.instance.getActiveAccount();

    if (userinfo != null) {
      this.logger.info('User authenticated successfully', {
        hasUserInfo: !!userinfo,
        isIframe: this.isIframe
      });

      this.AzureUserName = userinfo?.name ?? "";
      this.UserID = userinfo?.username ?? "";
      UserInfo.EmailID = userinfo?.username;
      UserInfo.UserName = userinfo?.name ?? "";
      this.isUserLoggedIn = true;
      //this.SaveRole();
      if (!localStorage.getItem('expiresOn') || !localStorage.getItem('userName') || !localStorage.getItem('homeAccountId') || !localStorage.getItem('token')) {
        this.logger.debug('Setting session data for authenticated user');
        // this.authService.setSessionData();
        await this.authService.setSessionData();
      }
      this.GetAllResponsibility();

      //this.GetRole(UserInfo.EmailID);
    }
    else {
      this.logger.warn('No authenticated user found, redirecting to login');
      this.isUserLoggedIn = false;
      //window.close()
      this.router.navigate(['/login']);
    }

    if (this.authService.ForceLogoutSession) {
      this.logger.session('expire', 'Force logout session detected');
      this.logout();
    }

    this.subscription.add(
      this.barcodeService.scannerMode$.subscribe(scannerMode => {
        if (scannerMode === ScannerMode.Closed && this.isInitialized) {
          setTimeout(() => {
            this.openBarcodeScanner();
          }, 100);
        }
      })
    );

    this.logger.debug('HomeComponent initialization completed');
  }

  ngOnDestroy() {
    this.logger.debug('HomeComponent destroying');
    this.subscription.unsubscribe();
    if (this.aquireTokenInterval) {
      clearInterval(this.aquireTokenInterval);
      this.logger.debug('Token refresh interval cleared');
    }
  }


  acquireTokenSilentlyOnInterval() {
    let timeIntervalInMs = 3300 * 1000; // renews the token for every 55 minutes
    //let timeIntervalInMs = 30 * 1000; // renews the token for every 30 seconds
    const now = new Date();
    var expiresOn = new Date(localStorage.getItem("expiresOn"));
    const refreshTime = new Date(expiresOn.setMinutes(expiresOn.getMinutes() - 5));

    this.logger.debug('Token refresh interval configured', {
      intervalMs: timeIntervalInMs,
      nextRefresh: new Date(new Date().setMilliseconds(timeIntervalInMs)).toString()
    });

    this.aquireTokenInterval = setInterval(() => {
      if (now > refreshTime) {
        const accessTokenRequest = {
          scopes: ["openid"],
          loginHint: localStorage.getItem('userName') || "",
          //forceRefresh: true
        }
        this.logger.session('refresh', 'Starting scheduled token refresh');
        this.msalService.acquireTokenSilent(accessTokenRequest).toPromise().then((accessTokenResponse) => {
          // call API
          let expiryTime = accessTokenResponse.expiresOn.toString();
          localStorage.setItem('expiresOn', expiryTime);
          localStorage.setItem('token', accessTokenResponse.accessToken);
          this.logger.session('refresh', 'Scheduled token refresh completed successfully');
        }).catch((error) => {
          this.logger.error("Error in scheduled token refresh", error, 'HomeComponent');
        });
      }
    }, timeIntervalInMs);
  }

  acquireTokenSilently() {
    const now = new Date();
    var expiresOn = new Date(this.msalService.instance.getActiveAccount()?.idTokenClaims?.exp * 1000)
    const refreshTime = new Date(expiresOn.setMinutes(expiresOn.getMinutes() - 5));

    if (now > refreshTime) {
      const accessTokenRequest = {
        scopes: ["openid", `${ActiveX.clientId}/.default`],
        loginHint: localStorage.getItem('userName') || "",
        //forceRefresh: true
      }
      this.logger.session('refresh', 'Starting immediate token refresh');
      this.msalService.acquireTokenSilent(accessTokenRequest).toPromise().then((accessTokenResponse) => {
        // call API
        let expiryTime = accessTokenResponse.expiresOn.toString();
        localStorage.setItem('expiresOn', expiryTime);
        localStorage.setItem('token', accessTokenResponse.accessToken);
        this.logger.session('refresh', 'Immediate token refresh completed successfully');
      }).catch((error) => {
        this.logger.error("Error in immediate token refresh", error, 'HomeComponent');
        if (error.errorCode === 'interaction_required' || error.errorCode === 'login_required') {
          this.logger.session('expire', 'Interactive login required, redirecting to login page');
          // Redirect user for interactive login
          let msalInstance: PublicClientApplication = this.msalService.instance as PublicClientApplication;
          msalInstance["browserStorage"].clear();
          this.router.navigate(['/login']);
        }
      });
    }
  }

  // logout() {
  //   const homeAccountId = localStorage.getItem('homeAccountId');
  //   const currentAccount = this.msalService.instance.getAccountByHomeId(homeAccountId);
  //   const logoutHint = currentAccount?.idTokenClaims?.login_hint;
  //   this.msalService.logoutPopup(
  //     {
  //       logoutHint: logoutHint,
  //       mainWindowRedirectUri: "/welcome",
  //     }
  //   );
  //   this.authService.clearLocalDataStorage();
  // }

  logout() {
    this.logger.info('User logout initiated');
    this.authService.clearLocalDataStorage();
    const currentAccount = this.msalService.instance.getActiveAccount();
    this.logger.session('clear', 'Redirecting to logout');
    this.msalService.logoutRedirect({
      account: currentAccount,
      postLogoutRedirectUri: "/welcome"
    });
  }
  CheckSalesReportSubMenuPermission() {
    if (this.CheckModule('Reports - Sales')) {
      return true;
    }
    else
      return false;
  }
  CheckStockReportSubMenuPermission() {
    if (this.CheckModule('Reports - Stock Availability') || this.CheckModule('Reports - Stock') || this.CheckModule('Reports - Store Wise Stock') ||
      this.CheckModule('Reports - Product Wise Stock') || this.CheckModule('Reports - Product Stock History') || this.CheckModule('Reports - Category Wise Stock')) {
      return true;
    }
    else
      return false;
  }
  CheckProductStockReportSubMenuPermission() {
    if (this.CheckModule('Reports - Product Stock Summary')) {
      return true;
    }
    else
      return false;
  }
  CheckPurchaseReportSubMenuPermission() {
    if (this.CheckModule('Reports - Purchase')) {
      return true;
    }
    else
      return false;
  }
  CheckProductionReportSubMenuPermission() {
    if (this.CheckModule('Reports - Production Planning') || this.CheckModule('Reports - Production Status') || this.CheckModule('Reports - Post Process')
      || this.CheckModule('Reports - Paste Consumption') || this.CheckModule('Reports - Yield') || this.CheckModule('Reports - Wastage')) {
      return true;
    }
    else
      return false;
  }
  openBarcodeScanner() {
    this.barcodeService.setScannerMode(ScannerMode.OverlayScanner);
  }
  openExternalDeviceScanner() {
    this.barcodeService.setScannerMode(ScannerMode.ExternalDevice);
  }

  openManualInput() {
    this.barcodeService.setScannerMode(ScannerMode.ManualEntry);
  }
}