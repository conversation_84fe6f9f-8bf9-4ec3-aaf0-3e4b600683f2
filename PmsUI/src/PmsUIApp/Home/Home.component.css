:host {
  display: flex;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.app-layout {
  height: 100vh;
}

.menu-sidebar {
  position: fixed;
  z-index: 10;
  min-height: 100vh;
  box-shadow: 2px 0 6px rgba(0, 21, 41, .35);
  height: 100vh;
  left: 0;
}

.sidebar-overflow {
  overflow-y: scroll;
  scrollbar-width: none;
  /* Firefox */
  -ms-overflow-style: none;
  /* Internet Explorer 10+ */
}

.noncollapsed-content {
  margin-left: 96px;
}

.collapsed-content {
  margin-left: 273px;
}

.header-trigger {
  height: 124px;
  padding: 20px 24px;
  font-size: 20px;
  cursor: pointer;
  transition: all .3s, padding 0s;
}

.noncollapse-trigger {
  margin-left: 71px;
}

.collapse-trigger {
  margin-left: 248px;
}

.trigger:hover {
  color: #1890ff;
}

.sidebar-logo {
  position: relative;
  height: 70px;
  padding-left: 24px;
  overflow: hidden;
  line-height: 65px;
  border-bottom: 1px solid white;
  background: #001529;
  transition: all .3s;
  margin-bottom: 5px;
}

.sidebar-logo_userfull {
  display: inline-block;
  height: 42px;
  width: 42px;
  vertical-align: middle;
  border-radius: 50%;
}

.sidebar-logo_usericon {
  display: inline-block;
  height: 25px;
  width: 25px;
  vertical-align: middle;
  border-radius: 50%;
}

.sidebar-logo h1 {
  display: inline-block;
  margin: 0 0 0 20px;
  color: #fff;
  font-weight: 600;
  font-size: 14px;
  font-family: Avenir, Helvetica Neue, Arial, Helvetica, sans-serif;
  vertical-align: middle;
}

nz-header {
  padding: 0;
  width: 100%;
  z-index: 2;
}

.app-header {
  position: relative;
  height: 64px;
  padding: 0;
  background: #fff;
  box-shadow: 0 1px 4px rgba(0, 21, 41, .08);
}

nz-content {
  margin: 24px;
}

.inner-content {
  padding: 24px;
  background: #fff;
  height: 100%;
}

.logout {
  display: inline-block;
  float: right;
  margin-right: 25px;
}

.logout span {
  margin-right: 15px;
}

.sideMenuHeading {
  color: #fff;
  font-weight: 600;
  border-bottom: 1px solid #fff;
}

/* Collapsed menu labels using pseudo-elements */
::ng-deep .ant-menu-inline-collapsed>.ant-menu-submenu.collapsed-menu-item::after {
  content: attr(data-label);
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: 4px;
  font-size: 9px;
  font-weight: 500;
  color: rgba(255, 255, 255, 0.85);
  text-align: center;
  line-height: 1;
  white-space: nowrap;
  pointer-events: none;
  z-index: 999;
  max-width: 70px;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
  width: auto;
}

/* Adjust submenu item positioning when collapsed */
::ng-deep .ant-menu-inline-collapsed>.ant-menu-submenu {
  position: relative !important;
  height: 60px !important;
  overflow: visible !important;
}

/* Ensure proper spacing for collapsed menu items */
::ng-deep .ant-menu-inline-collapsed>.ant-menu-submenu>.ant-menu-submenu-title {
  height: 60px !important;
  line-height: 60px !important;
  position: relative !important;
  padding: 0 24px !important;
  text-align: center !important;
}

/* Position the icon properly without breaking layout */
::ng-deep .ant-menu-inline-collapsed>.ant-menu-submenu>.ant-menu-submenu-title .anticon {
  font-size: 16px !important;
  margin: 0 auto !important;
  display: inline-block !important;
  vertical-align: middle !important;
}

/* Hover effect for collapsed menu labels */
::ng-deep .ant-menu-inline-collapsed>.ant-menu-submenu.collapsed-menu-item:hover::after {
  color: #1890ff !important;
}

/* Responsive adjustments for smaller screens */
@media (max-width: 768px) {
  .collapsed-menu-label {
    font-size: 9px;
    bottom: 2px;
    max-width: 50px;
  }
}