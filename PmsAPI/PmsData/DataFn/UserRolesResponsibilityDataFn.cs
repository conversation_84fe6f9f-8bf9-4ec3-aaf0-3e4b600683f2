﻿using PmsEntity.ViewModel;
using System;
using System.Collections.Generic;
using System.Text;
using System.Linq;
using PmsData.Models;
using System.Net;
using PmsCommon;
using Microsoft.EntityFrameworkCore;

namespace PmsData.DataFn
{
    public class UserRolesResponsibilityDataFn
    {
        public GlobalDataEntity GlobalData;
        public UserRolesResponsibilityDataFn(GlobalDataEntity gd)
        {
            GlobalData = gd;
        }
        public List<ResponsibilityMasterVm> GetAllResponsibility()
        {
            List<ResponsibilityMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.ResponsibilityMasters
                       select new ResponsibilityMasterVm
                       {
                           ResponsibilityId = a.ResponsibilityId,
                           ResponsibilityCode = a.ResponsibilityCode,
                           Module = a.Module,
                           ResponsibilityName = a.ResponsibilityName,
                           ResponsibilityDescripton = a.ResponsibilityDescripton,
                           AddedBy = a.AddedBy,
                           AddedDate = a.AddedDate
                       }).OrderBy(x => x.Module).ToList();
            }
            return res;
        }
        public List<ResponsibilityMasterVm> GetAllResponsibilityWithRoles()
        {
            List<ResponsibilityMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.ResponsibilityMasters
                       select new ResponsibilityMasterVm
                       {
                           ResponsibilityId = a.ResponsibilityId,
                           ResponsibilityCode = a.ResponsibilityCode,
                           Module = a.Module,
                           ResponsibilityName = a.ResponsibilityName,
                           ResponsibilityDescripton = a.ResponsibilityDescripton,
                           AddedBy = a.AddedBy,
                           AddedDate = a.AddedDate,
                           AssignedRoles = string.Join(", ", (from urrm in db.UserRoleResponsibilityMappingTables
                                                              join urm in db.UserRoleMasters on urrm.UserRoleId equals urm.UserRoleId
                                                              where urrm.ResponsibilityId == a.ResponsibilityId
                                                              select urm.UserRoleName).ToList()),

                       }).OrderBy(x => x.Module).ToList();
            }
            return res;
        }
        public ApiFunctionResponseVm AddUpdateResponsibility(ResponsibilityMasterVm br)
        {
            //string[] AllAccessRoles = { "Admin", "Super Admin" };
            //int count = 0;

            using (var db = new Models.pmsdbContext())
            {
                var userroles = (from a in db.UserRoleMasters
                                 join urmmap in db.UsernameUserRoleMappingTables on a.UserRoleId equals urmmap.UserRoleId
                                 where urmmap.Username == GlobalData.loggedInUser
                                 select new UserRoleMasterVm
                                 {
                                     UserRoleId = a.UserRoleId,
                                     UserRoleName = a.UserRoleName,
                                 }).ToList();


                if (!userroles.Any(x => x.UserRoleName.ToLowerInvariant() == "super admin"))
                {
                    return new ApiFunctionResponseVm(HttpStatusCode.Unauthorized, "Only Super Admins can create responsibilities.");
                }
                else
                {
                    ResponsibilityMaster res = new ResponsibilityMaster();
                    var rmRec = db.ResponsibilityMasters.FirstOrDefault(x => x.ResponsibilityId == br.ResponsibilityId);

                    if (rmRec == null)
                    {
                        res.ResponsibilityCode = br.ResponsibilityCode;
                        res.ResponsibilityName = br.ResponsibilityName;
                        res.Module = br.Module;
                        res.ResponsibilityDescripton = br.ResponsibilityDescripton;
                        res.AddedBy = GlobalData.loggedInUser;
                        res.AddedDate = System.DateTime.Now;
                        db.ResponsibilityMasters.Add(res);

                    }
                    else
                    {
                        rmRec.ResponsibilityCode = br.ResponsibilityCode;
                        rmRec.ResponsibilityName = br.ResponsibilityName;
                        rmRec.Module = br.Module;
                        rmRec.ResponsibilityDescripton = br.ResponsibilityDescripton;
                        rmRec.AddedBy = GlobalData.loggedInUser;
                    }
                    db.SaveChanges();

                    if (res.ResponsibilityId > 0)
                    {
                        var UserRoleId = db.UserRoleMasters.FirstOrDefault(I => I.UserRoleName == "Super Admin");
                        //var ResponsibilityId = db.ResponsibilityMasters.Max(x => x.ResponsibilityId);

                        UserRoleResponsibilityMappingTable UserMap = new UserRoleResponsibilityMappingTable();
                        UserMap.UserRoleId = UserRoleId.UserRoleId;
                        UserMap.ResponsibilityId = res.ResponsibilityId;
                        UserMap.AddedBy = GlobalData.loggedInUser;
                        UserMap.AddedDate = System.DateTime.Now;
                        db.UserRoleResponsibilityMappingTables.Add(UserMap);
                    }

                    db.SaveChanges();

                    return new ApiFunctionResponseVm(HttpStatusCode.OK, "Responsibility Saved Successfully.");
                }
            }
        }

        public List<UserRoleMasterVm> GetAllUserRoles()
        {
            List<UserRoleMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.UserRoleMasters
                       select new UserRoleMasterVm
                       {
                           UserRoleId = a.UserRoleId,
                           UserRoleCode = a.UserRoleCode,
                           UserRoleName = a.UserRoleName,

                           UserRoleDescripton = a.UserRoleDescripton,
                           AddedBy = a.AddedBy,
                           AddedDate = a.AddedDate,
                           Responsibilities = (from rm in db.ResponsibilityMasters
                                               join rmmap in db.UserRoleResponsibilityMappingTables on rm.ResponsibilityId equals rmmap.ResponsibilityId
                                               where rmmap.UserRoleId == a.UserRoleId
                                               select new ResponsibilityMasterVm
                                               {
                                                   ResponsibilityId = rm.ResponsibilityId,
                                                   ResponsibilityCode = rm.ResponsibilityCode,
                                                   ResponsibilityName = rm.ResponsibilityName,
                                                   ResponsibilityDescripton = rm.ResponsibilityDescripton,
                                                   Module = rm.Module,
                                                   AddedBy = rm.AddedBy,
                                                   AddedDate = rm.AddedDate
                                               }).OrderBy(x => x.ResponsibilityId).ToList(),
                           AssignedUsers = (from um in db.UserMasters
                                            join urmmap in db.UsernameUserRoleMappingTables on um.Email equals urmmap.Username
                                            where urmmap.UserRoleId == a.UserRoleId && um.Status == "Active"
                                            select new UserMasterVm
                                            {
                                                UserId = um.UserId,
                                                Name = um.Name,
                                                Contact = um.Contact,
                                                Email = um.Email,
                                                Address = um.Address,
                                            }).OrderBy(x => x.Name).ToList(),
                       }).OrderBy(x => x.UserRoleName).ToList();
            }
            return res;
        }

        public List<UserMasterVm> GetAllUserData()
        {
            List<UserMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from um in db.UserMasters
                       where um.Status == "Active"
                       select new UserMasterVm
                       {
                           UserId = um.UserId,
                           Name = um.Name,
                           Contact = um.Contact,
                           Email = um.Email,
                           Address = um.Address,
                           UserRole = (from a in db.UserRoleMasters
                                       join urmmap in db.UsernameUserRoleMappingTables on a.UserRoleId equals urmmap.UserRoleId
                                       where urmmap.Username == um.Email
                                       select new UserRoleMasterVm
                                       {
                                           UserRoleId = a.UserRoleId,
                                           UserRoleCode = a.UserRoleCode,
                                           UserRoleName = a.UserRoleName,
                                           UserRoleDescripton = a.UserRoleDescripton,
                                       }).OrderBy(x => x.UserRoleId).ToList()
                       }).OrderBy(x => x.Email).ToList();

            }
            return res;
        }

        public List<UserMasterVm> GetAllUsersByResposibilityId(long respId)
        {
            List<UserMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from um in db.UserMasters
                       join urmmap in db.UsernameUserRoleMappingTables on um.Email equals urmmap.Username
                       join usr in db.UserRoleMasters on urmmap.UserRoleId equals usr.UserRoleId
                       join urresmmap in db.UserRoleResponsibilityMappingTables on usr.UserRoleId equals urresmmap.UserRoleId
                       join resp in db.ResponsibilityMasters on urresmmap.ResponsibilityId equals resp.ResponsibilityId
                       where um.Status == "Active" && resp.ResponsibilityId == respId
                       select new UserMasterVm
                       {
                           UserId = um.UserId,
                           Name = um.Name,
                           Contact = um.Contact,
                           Email = um.Email,
                           Address = um.Address,
                       }).Distinct().OrderBy(x => x.Email).ToList();
            }
            return res;
        }

        public List<UserRoleMasterVm> GetAllUserRolesByUserName(string username)
        {
            List<UserRoleMasterVm> res = null;
            using (var db = new Models.pmsdbContext())
            {
                res = (from a in db.UserRoleMasters
                       join urmmap in db.UsernameUserRoleMappingTables on a.UserRoleId equals urmmap.UserRoleId
                       where urmmap.Username == username
                       select new UserRoleMasterVm
                       {
                           UserRoleId = a.UserRoleId,
                           UserRoleCode = a.UserRoleCode,
                           UserRoleName = a.UserRoleName,
                           UserRoleDescripton = a.UserRoleDescripton,
                           AddedBy = a.AddedBy,
                           AddedDate = a.AddedDate,
                           Responsibilities = (from rm in db.ResponsibilityMasters
                                               join rmmap in db.UserRoleResponsibilityMappingTables on rm.ResponsibilityId equals rmmap.ResponsibilityId
                                               where rmmap.UserRoleId == a.UserRoleId
                                               select new ResponsibilityMasterVm
                                               {
                                                   ResponsibilityId = rm.ResponsibilityId,
                                                   ResponsibilityCode = rm.ResponsibilityCode,
                                                   ResponsibilityName = rm.ResponsibilityName,
                                                   ResponsibilityDescripton = rm.ResponsibilityDescripton,
                                                   Module = rm.Module,
                                                   AddedBy = rm.AddedBy,
                                                   AddedDate = rm.AddedDate
                                               }).OrderBy(x => x.ResponsibilityId).ToList(),
                       }).OrderBy(x => x.UserRoleName).ToList();
            }
            return res;
        }
        public ApiFunctionResponseVm AddUpdateUserRole(UserRoleMasterVm br)
        {
            using (var db = new Models.pmsdbContext())
            {
                UserRoleMaster res = new UserRoleMaster();
                var rmRec = db.UserRoleMasters.FirstOrDefault(x => x.UserRoleId == br.UserRoleId);
                if (rmRec == null)
                {
                    res.UserRoleCode = br.UserRoleCode;
                    res.UserRoleName = br.UserRoleName;
                    res.UserRoleDescripton = br.UserRoleDescripton;
                    res.AddedBy = GlobalData.loggedInUser;
                    res.AddedDate = System.DateTime.Now;
                    db.UserRoleMasters.Add(res);
                }
                else
                {
                    rmRec.UserRoleCode = br.UserRoleCode;
                    rmRec.UserRoleName = br.UserRoleName;
                    rmRec.UserRoleDescripton = br.UserRoleDescripton;
                    //rmRec.AddedBy = GlobalData.loggedInUser;

                }
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, res);
            }
        }

        public ApiFunctionResponseVm AddUser(UserMasterVm br)
        {
            using (var db = new Models.pmsdbContext())
            {
                var rmRec = db.UserMasters.FirstOrDefault(x => x.Email == br.Email);
                if (rmRec == null)
                {
                    UserMaster res = new UserMaster();
                    res.Name = br.Name;
                    res.Contact = br.Contact;
                    res.Email = br.Email;
                    res.Address = br.Address;
                    res.Status = "Active";
                    db.UserMasters.Add(res);
                }
                else
                {
                    rmRec.Name = br.Name;
                    rmRec.Contact = br.Contact;
                    rmRec.Email = br.Email;
                    rmRec.Address = br.Address;
                }
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran successful");
            }
        }
        public ApiFunctionResponseVm RemoveUser(string username)
        {
            using (var db = new Models.pmsdbContext())
            {
                var rmRec = db.UserMasters.FirstOrDefault(x => x.Email.Contains(username));
                if (rmRec == null)
                    return new ApiFunctionResponseVm(HttpStatusCode.OK, "User not found with username: " + username);
                var rmRecMap = db.UsernameUserRoleMappingTables.Where(x => x.Username == rmRec.Email).ToList();
                if (rmRecMap != null)
                    db.UsernameUserRoleMappingTables.RemoveRange(rmRecMap);
                if (rmRec.AdobjectId != null)
                {
                    ActiveDirectoryDataFn ADOperations = new ActiveDirectoryDataFn();
                    var ADUserDisable = ADOperations.DisableADUserAsync(rmRec.AdobjectId);
                }

                rmRec.Status = "Inactive";
                rmRec.DisabledBy = GlobalData.loggedInUser;
                rmRec.DisabledDate = System.DateTime.Now;
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran successful");
            }
        }

        public ApiFunctionResponseVm AddUpdateRoleResposibilityMapping(List<UserRoleResponsibilityMappingTableVm> br)
        {
            using (var db = new Models.pmsdbContext())
            {
                List<UserRoleResponsibilityMappingTable> stAllList = db.UserRoleResponsibilityMappingTables.Where(x => x.UserRoleId == br[0].UserRoleId).ToList();
                if (stAllList != null)
                {
                    db.UserRoleResponsibilityMappingTables.RemoveRange(stAllList);
                    db.SaveChanges();
                }

                foreach (var item in br)
                {
                    UserRoleResponsibilityMappingTable spt = new UserRoleResponsibilityMappingTable();
                    spt.UserRoleId = item.UserRoleId;
                    spt.ResponsibilityId = item.ResponsibilityId;
                    spt.AddedBy = GlobalData.loggedInUser;
                    spt.AddedDate = System.DateTime.Now;
                    db.UserRoleResponsibilityMappingTables.Add(spt);
                }
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
            }
        }

        public ApiFunctionResponseVm AddUpdateUsernameUserRoleMapping(List<UsernameUserRoleMappingTableVm> br)
        {
            using (var db = new Models.pmsdbContext())
            {
                foreach (var item in br)
                {
                    var role = db.UserRoleMasters.Where(x => x.UserRoleId == item.UserRoleId).FirstOrDefault().UserRoleName;
                    if (role == "Super Admin" && GlobalData.loggedInUser.Split("@").Last() != "misayinnovations.com")
                    {
                        return new ApiFunctionResponseVm(HttpStatusCode.Forbidden, "Super Admin Role can only be given by Misay Innovations Admins.");
                    }
                }
                List<UsernameUserRoleMappingTable> stAllList = db.UsernameUserRoleMappingTables.Where(x => x.Username == br[0].Username).ToList();
                if (stAllList != null)
                {
                    db.UsernameUserRoleMappingTables.RemoveRange(stAllList);
                    db.SaveChanges();
                }
                foreach (var item in br)
                {
                    UsernameUserRoleMappingTable spt = new UsernameUserRoleMappingTable();
                    spt.UserRoleId = item.UserRoleId;
                    spt.Username = item.Username;
                    spt.AddedBy = GlobalData.loggedInUser;
                    spt.AddedDate = System.DateTime.Now;
                    db.UsernameUserRoleMappingTables.Add(spt);
                }
                db.SaveChanges();
                return new ApiFunctionResponseVm(HttpStatusCode.OK, "Function ran Successfully");
            }
        }
        public List<string> GetAllUserExceptionForceLogout()
        {
            using var db = new Models.pmsdbContext();
            return db.UserExceptionForceLogoutTables
            .Include(x => x.User)
            .Where(x => x.IsActive == true)
            .Select(x => x.User.Email)
            .ToList();
        }
    }
}
